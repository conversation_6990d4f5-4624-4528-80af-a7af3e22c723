package tech.pu12cnt.user.service.impl;

import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.password.PasswordEncoder;
import tech.pu12cnt.user.mapper.UserMapper;
import tech.pu12cnt.user.entity.User;
import tech.pu12cnt.user.service.UserService;
import tech.pu12cnt.common.exception.BusinessException;
import tech.pu12cnt.common.enums.ResponseCode;
import tech.pu12cnt.common.utils.JwtUtils;
import org.springframework.transaction.annotation.Transactional;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;


@Service
public class UserServiceImpl extends ServiceImpl<UserMapper, User> implements UserService{

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private PasswordEncoder passwordEncoder;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public User register(User newUser) {
        validateUserUniqueness(newUser);
        newUser.setPassword(passwordEncoder.encode(newUser.getPassword()));
        this.save(newUser);
        return newUser;
    
    }

    @Override
    public String login(String username, String password) {
        User user = getUserByUsername(username);
        // 2. 判断用户是否存在
        if (user == null) {
            throw new BusinessException(ResponseCode.USER_NOT_FOUND);
        }
        // 3. 判断密码是否正确
        if (!passwordEncoder.matches(password, user.getPassword())) {
            throw new BusinessException(ResponseCode.USER_PASSWORD_ERROR);
        }
        // 4. 生成JWT Token
        return JwtUtils.generateToken(user.getId(), user.getUsername());
    }

    private void validateUserUniqueness(User user) {
        if (existsByUsername(user.getUsername())) {
            throw new BusinessException(ResponseCode.USER_ALREADY_EXISTS);
        }
        if (existsByEmail(user.getEmail())) {
            throw new BusinessException(ResponseCode.USER_EMAIL_ALREADY_EXISTS);
        }
        if (existsByPhone(user.getPhone())) {
            throw new BusinessException(ResponseCode.USER_PHONE_ALREADY_EXISTS);
        }

    }
    @Override
    public User getUserByUsername(String username) {
        return userMapper.findByUsername(username);
    }

    @Override
    public boolean existsByUsername(String username) {
        return this.count(new QueryWrapper<User>().eq("username", username)) > 0;
    }

    @Override
    public boolean existsByEmail(String email) {
        return this.count(new QueryWrapper<User>().eq("email", email)) > 0;
    }

    @Override
    public boolean existsByPhone(String phone) {
        return this.count(new QueryWrapper<User>().eq("phone", phone)) > 0;
    }

}