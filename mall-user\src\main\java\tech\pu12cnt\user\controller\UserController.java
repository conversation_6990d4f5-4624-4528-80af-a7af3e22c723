package tech.pu12cnt.user.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.*;
import tech.pu12cnt.common.result.Result;
import jakarta.validation.Valid;
import tech.pu12cnt.user.entity.User;
import tech.pu12cnt.user.service.UserService;
import tech.pu12cnt.user.dto.LoginRequest;
import tech.pu12cnt.common.exception.BusinessException;
import tech.pu12cnt.common.enums.ResponseCode;
import lombok.extern.slf4j.Slf4j;



@Slf4j
@RestController
@RequestMapping("/api/user")
public class UserController {
    @Autowired
    private UserService userService;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @PostMapping("/register")
    public Result<User> register(@Valid @RequestBody User user) {
        log.info("用户注册：username={}", user.getUsername());

        User registeredUser = userService.register(user);

        log.info("用户注册成功：userId={}, username={}",
                registeredUser.getId() , registeredUser.getUsername());

        return Result.success(registeredUser);
    }

    @PostMapping("/login")
    public Result<String> login(@RequestBody LoginRequest request) {
        String token = userService.login(request.getUsername(), request.getPassword());
        return Result.success(token);
    }

    @GetMapping("/redis-test")
    public Result<String> testRedis() {
        try{
            String key = "test:redis:" + System.currentTimeMillis();
            User testUser = new User();
            testUser.setUsername("redis-test-user");
            testUser.setEmail("<EMAIL>");
            testUser.setPhone("13800138000");
            testUser.setAvatar("http://test.com/avatar.jpg");
            
            
            redisTemplate.opsForValue().set(key, testUser);
            
        
            User result = (User) redisTemplate.opsForValue().get(key);
            
            if(result == null){
                return Result.error("Redis读取失败：数据为空");
            }
            return Result.success("Redis测试成功：" + result.toString());
        }catch(Exception e){

            log.error("Redis测试失败", e);
            return Result.error("Redis连接失败：" + e.getMessage());
        }

    
    
    }


    @GetMapping("/info/{id}")
    public Result<User> getUserInfo(@PathVariable Long id) {
        if(id == null || id <= 0){
            throw new BusinessException(ResponseCode.PARAM_ERROR, "用户ID无效");
        }

        User user = userService.getById(id);
        if(user == null){
            throw new BusinessException(ResponseCode.USER_NOT_FOUND);
        }

        return Result.success(user);
    }

    @PutMapping("/update")
    public Result<User> updateUserInfo(@Valid @RequestBody User user) {

    if (user.getId() == null || user.getId() <= 0) {
        throw new BusinessException(ResponseCode.PARAM_ERROR, "用户ID不能为空");
    }
    
    User existingUser = userService.getById(user.getId());
    if (existingUser == null) {
        throw new BusinessException(ResponseCode.USER_NOT_FOUND);
    }
    
    boolean success = userService.updateById(user);
    if (!success) {
        throw new BusinessException(ResponseCode.INTERNAL_SERVER_ERROR, "更新失败");
    }
    
    User updatedUser = userService.getById(user.getId());
    return Result.success(updatedUser);
    }
   
}