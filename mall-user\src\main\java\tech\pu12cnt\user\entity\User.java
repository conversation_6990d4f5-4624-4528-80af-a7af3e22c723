package tech.pu12cnt.user.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import tech.pu12cnt.common.entity.BaseEntity;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.Pattern;
import org.hibernate.validator.constraints.Length;


@Data
@TableName("user") // 表名，默认与类名相同，如果不同，需要指定表名
public class User extends BaseEntity {

    private Integer status;
    
    @NotBlank(message = "用户名不能为空")
    @Length(min = 3, max = 20, message = "用户名长度必须在3-20之间")
    private String username;

    @NotBlank(message = "密码不能为空")
    @Length(min = 6, max = 20, message = "密码长度必须在6-20之间")
    private String password;

    @Email(message = "邮箱格式不正确")
    private String email;

    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    private String phone;

    @Length(max = 200, message = "头像URL长度不能超过200")
    private String avatar;
}
